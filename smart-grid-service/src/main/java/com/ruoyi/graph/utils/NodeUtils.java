package com.ruoyi.graph.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.plan.vo.PlanOperateData;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.bo.LayNodeBo;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.model.HwgBayBo;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.util.coordinates.CoordinateConverter.convertGeometryToDoubleList;
import static com.ruoyi.util.coordinates.CoordinateConverter.*;

public class NodeUtils {

    /**
     * 节点集合转返回到NodeVo
     */
    public static ArrayList<NodeVo> toNodeVos(List<Node> nodes) {
        if (nodes == null) {
            return null;
        }
        ArrayList<NodeVo> result = new ArrayList<>();
        for (Node node : nodes) {
            // 电网设备的ID

            NodeVo nodeVo = new NodeVo(getPsrNodeId(node), node.getPsrId(), node.getPsrType(), node.getPsrName(), node.isEdge(), node.getType(), node.getShapeKey());

            nodeVo.setPorts(node.getPorts());
            nodeVo.setSourcePort(node.getSourcePort());
            nodeVo.setTargetPort(node.getTargetPort());
            // 处理坐标
            Geometry geometry = node.getGeometry();
            if (geometry != null) {
                if (node.isEdge()) {
                    List<Double> doubleList = convertGeometryToDoubleList(geometry);
                    // 转换为N/2 x 2的二维数组（假设元素数量是偶数）
                    double[][] coordinates = new double[doubleList.size() / 2][2];
                    for (int i = 0; i < doubleList.size(); i += 2) {
                        coordinates[i / 2][0] = doubleList.get(i);    // 经度
                        coordinates[i / 2][1] = doubleList.get(i + 1);  // 纬度
                    }
                    nodeVo.setCoordinates(coordinates);
                } else {
                    nodeVo.setCoordinates(convertGeometryToDoubleList(geometry));
                }
            }

            // 处理子级
            if (CollectionUtils.isNotEmpty(node.getChildren())) {
                nodeVo.setChildrenIds(
                        node.getChildren().stream().map(n -> getPsrNodeId(n)).collect(Collectors.toList())
                );
            }

            // 处理父级
            if (node.getParent() != null) {
                nodeVo.setParentId(getPsrNodeId(node.getParent()));
            }

            // 处理设备的边
            if (CollectionUtils.isNotEmpty(node.getEdges())) {
                List<Node> edges = node.getEdges().stream().filter(Objects::nonNull).collect(Collectors.toList());
                nodeVo.setEdgeIds(
                        edges.stream().map(n -> getPsrNodeId(n)).collect(Collectors.toList())
                );
            }
            if (node.isEdge()) {
                nodeVo.setLineType(node.getLineType());
            }

            nodeVo.setProperties(node.getProperties());

            nodeVo.setSourceId(node.getSource() != null ? getPsrNodeId(node.getSource()) : null);
            nodeVo.setTargetId(node.getTarget() != null ? getPsrNodeId(node.getTarget()) : null);
            result.add(nodeVo);
        }
        return result;
    }

    /**
     * nodeVos转nodes
     */
    public static List<Node> toNodes(List<NodeVo> nodeVos) {
        if (nodeVos == null) {
            return null;
        }
        ArrayList<Node> result = new ArrayList<>();
        HashMap<String, Node> nodeMap = new HashMap<>();

        for (NodeVo nodeVo : nodeVos) {
            // 电网设备的ID

            Node node = new Node(nodeVo.getId(), nodeVo.getPsrId(), nodeVo.getPsrType(), nodeVo.getName(), nodeVo.isEdge(), nodeVo.getType(), nodeVo.getLineType(), nodeVo.getShapeKey());

            // 处理坐标
            Object coords = nodeVo.getCoordinates();
            if (coords != null) {
                if (node.isEdge()) {
                    LineString lineString = null;
                    if (coords instanceof double[][]) {
                        // 情况1：原生二维数组 double[][]
                        lineString = toLineString((double[][]) coords);
                    } else if (coords instanceof JSONArray) {
                        // 情况2：FastJSON 的 JSONArray
                        jsonArrayToLineString((JSONArray) coords);
                    } else if (coords instanceof List && !((List<?>) coords).isEmpty() && ((List<?>) coords).get(0) instanceof List) {
                        //情况3：(List<List<Double>>)
                        lineString = toLineString((List<List<Double>>) coords);
                    } else {
                        node.setGeometry(null);
                    }
                    node.setGeometry(lineString);
                } else {
                    Point point = null;
                    if (coords instanceof List) {
                        List<?> coordsList = (List<?>) coords;
                        if (coordsList.isEmpty()) {
                            node.setGeometry(null);
                        }
                        Object firstElement = coordsList.get(0);
                        if (firstElement instanceof BigDecimal) {
                            List<Double> doubleList = new ArrayList<>();
                            // 处理 BigDecimal 列表
                            BigDecimal x = (BigDecimal) coordsList.get(0);
                            BigDecimal y = (BigDecimal) coordsList.get(1);
                            doubleList.add(x.doubleValue());
                            doubleList.add(y.doubleValue());
                            point = toPoint(doubleList);
                        } else if (firstElement instanceof Double) {
                            // 处理 Double 列表
                            List<Double> doubleList = (List<Double>) coordsList;
                            point = toPoint(doubleList);

                        } else {
                            node.setGeometry(null);
                        }
                    } else {
                        node.setGeometry(null);
                    }
                    node.setGeometry(point);
                }
            }
            nodeMap.put(node.getId(), node);
            result.add(node);
        }
        for (NodeVo nodeVo : nodeVos) {
            String parentId = nodeVo.getParentId();
            List<String> childrenIds = nodeVo.getChildrenIds();
            String sourceId = nodeVo.getSourceId();
            String targetId = nodeVo.getTargetId();
            List<String> edgeIds = nodeVo.getEdgeIds();

            Node node = nodeMap.get(nodeVo.getId());
            // 父
            if (StringUtils.isNotBlank(parentId)) {
                node.setParent(nodeMap.get(parentId));
            }

            // 子级
            if (CollectionUtils.isNotEmpty(childrenIds)) {
                node.setChildren(childrenIds.stream().map(id -> nodeMap.get(id)).collect(Collectors.toList()));
            }

            // 原ID
            if (StringUtils.isNotBlank(sourceId)) {
                node.setSource(nodeMap.get(sourceId));
            }

            if (StringUtils.isNotBlank(targetId)) {
                node.setTarget(nodeMap.get(targetId));
            }

            node.setProperties(nodeVo.getProperties());

            // 链接边
            if (CollectionUtils.isNotEmpty(edgeIds)) {
                node.setEdges(edgeIds.stream().map(id -> nodeMap.get(id)).collect(Collectors.toList()));
            }
        }

        return result;
    }

    /**
     * 复制节点集合
     *
     * @param nodes
     * @return
     */
    public static List<Node> copyNodes(List<Node> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return nodes;
        }
        ArrayList<NodeVo> nodeVos = toNodeVos(nodes);
        return toNodes(nodeVos);
    }

    /**
     * znap电网节点的ID是 PD_开头的ID  我们这里获取解构后的ID（psrId）
     */
    public static String getPsrNodeId(Node node) {
        String id = ZnapUtils.parsePsrId(node.getId());
        return id != null ? id : node.getId();
    }

    /**
     * 去重
     */
    public static List<Node> duplicateNodes(List<Node> nodes) {
        List<Node> result = new ArrayList<>();
        Set<String> seenKeys = new HashSet<>();

        for (Node node : nodes) {
            if (seenKeys.add(node.getId())) {
                result.add(node);
            }
        }
        return result;
    }


    public static ArrayList<SegBetweenVo> toSegBetweenVoList(List<SegBetween> segBetweenList) {
        if (segBetweenList == null) {
            return null;
        }
        ArrayList<SegBetweenVo> result = new ArrayList<>();

        for (SegBetween segBetween : segBetweenList) {
            SegBetweenVo segBetweenVo = new SegBetweenVo(segBetween.getId(), segBetween.getStartPsrId(), segBetween.getStartPsrType(), segBetween.getStartPsrName(), segBetween.getEndPsrId(), segBetween.getEndPsrType(), segBetween.getEndPsrName());

            segBetweenVo.setNodes(toNodeVos(segBetween.getNodes()));
            segBetweenVo.setMainOtherNodes(toNodeVos(segBetween.getMainOtherNodes()));
            segBetweenVo.setMainAllOtherNodes(toNodeVos(segBetween.getMainAllOtherNodes()));

            result.add(segBetweenVo);
        }
        return result;
    }

    public static void loopNode(Node startNode, HashMap<String, Boolean> useNodeMap) {
        Node currentNode = startNode;

        // 递归获取查找
        while (currentNode != null) {
            useNodeMap.put(currentNode.getId(), true);
            // 新增当前的线路
            boolean isEdge = currentNode.isEdge();
            Node nextNode = null;
            List<Node> edges = null;
            if (isEdge) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = useNodeMap.containsKey(source.getId()) ? target : source;
                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }
            } else { // 设备节点
                // 当前设备有那些边 我们继续往下递归
                edges = currentNode.getEdges();
            }
            if (edges != null) {
                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());
                // 调度继续循环
                for (Node edge : edges) {
                    loopNode(edge, useNodeMap);
                }
                nextNode = null;
            }
            currentNode = nextNode;
        }
    }

    /**
     * 回溯法递归生成所有有效的分割组合
     *
     * @param current 当前处理的位置（前缀和索引）
     * @param prefix  前缀和数组
     * @param path    当前路径（存储分割点）
     * @param result  所有有效组合的集合
     */
    public static void backtrack(int current, int end, int[] prefix, List<Integer> path, List<List<Integer>> result, int max) {
        // 添加当前分割点（路径记录的是前缀和索引，实际位置需转换）
        path.add(current);

        // 终止条件：到达末尾（最后一个分支之后）
        if (current == end) {
            // 转换为实际开关位置（排除首尾的0和n）
            List<Integer> validCombination = new ArrayList<>();
            for (int i = 1; i < path.size() - 1; i++) {
                validCombination.add(path.get(i));
            }
            result.add(validCombination);
            path.remove(path.size() - 1);
            return;
        }

        // 尝试所有可能的下一个分割点
        for (int next = current + 1; next <= end; next++) {
            int sum = prefix[next] - prefix[current];
            if (sum >= max) break; // 超过阈值，后续更大区间也会超，直接终止
            backtrack(next, end, prefix, path, result, max);
        }

        // 回溯，移除当前分割点
        path.remove(path.size() - 1);
    }

    public static boolean hasNode(List<ArrayList<Node>> nodeList, Node target) {
        if (nodeList == null) {
            return false;
        }
        for (ArrayList<Node> nodes : nodeList) {
            for (Node node : nodes) {
                if (node.equals(target)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static Node findNode(List<Node> nodeList, Node target) {
        if (nodeList == null) {
            return null;
        }
        for (Node node : nodeList) {
            if (node.equals(target)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 查找节点出现的下标
     */
    public static int findNodeIndex(List<Node> nodeList, Node target) {
        if (nodeList == null) {
            return -1;
        }

        for (int i = 0; i < nodeList.size(); i++) {
            if (nodeList.get(i).equals(target)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 截取数组
     *
     * @return
     */
    public static List<Node> subLiceNode(List<Node> nodeList, Node startNode, Node endNode) {

        if (nodeList == null) {
            return null;
        }

        if (startNode == null && endNode == null) {
            return null;
        }
        int startIndex = startNode != null ? findNodeIndex(nodeList, startNode) : 0;
        int endIndex = endNode != null ? findNodeIndex(nodeList, endNode) : nodeList.size();
        return nodeList.subList(startIndex, endIndex);
    }

    public static Node findNode(List<Node> nodeList, Function<Node, Boolean> equalFunc) {
        if (nodeList == null) {
            return null;
        }
        for (Node node : nodeList) {
            if (equalFunc.apply(node)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 提取二维列表中长度最小的所有子列表
     *
     * @param lists 输入的二维列表
     * @param <T>   列表元素类型
     * @return 长度最小的子列表集合
     */
    public static <T> List<List<T>> extractMinLengthLists(List<List<T>> lists) {
        List<List<T>> result = new ArrayList<>();
        if (lists == null || lists.isEmpty()) {
            return result; // 处理空输入
        }

        int minLength = Integer.MAX_VALUE;
        for (List<T> list : lists) {
            if (list == null) continue; // 跳过 null 子列表
            int currentLength = list.size();

            if (currentLength < minLength) {
                minLength = currentLength;
                result.clear(); // 发现更小长度，清空之前的结果
                result.add(list);
            } else if (currentLength == minLength) {
                result.add(list); // 相同最小长度，添加到结果
            }
        }
        return result;
    }

    /**
     * 获取中间杆塔  如果是偶数 比如 4  那么会返回第2个
     */
    public static Node getMiddlePole(List<Node> nodes) {
        // 筛选出所有 type='aa' 的节点
        List<Node> filteredNodes = nodes.stream()
                .filter(Node::isPole)
                .collect(Collectors.toList());
        if (filteredNodes.size() == 0) {
            return null;
        }
        int mid = filteredNodes.size() / 2;

        if (filteredNodes.size() % 2 == 1) { // 基数
            return filteredNodes.get(mid);
        } else {
            return filteredNodes.get(mid);
        }
    }

    /**
     * 将节点集合转为方案的操作数据的json字符串
     */
    public static PlanOperateData toPlanOperateData(List<Node> nodes) {
        List<NodeVo> nodeVos = toNodeVos(nodes);
        List<NodeVo> edges = new ArrayList<>();
        List<NodeVo> devices = new ArrayList<>();
        for (NodeVo node : nodeVos) {
            if (node.isEdge()) {
                edges.add(node);
            } else {
                devices.add(node);
            }
        }

        return new PlanOperateData(edges, devices);
    }

    /**
     * 将方案的json的PlanOperate换原成List<node>
     */
    public static List<Node> planOperateJsonToNodes(String planOperateJson) throws JsonProcessingException {
        //将json还原成PlanOperateData
        ObjectMapper mapper = new ObjectMapper();
        PlanOperateData data = mapper.readValue(planOperateJson, PlanOperateData.class);
        //如果转换失败则返回空
        if (data == null) {
            return null;
        }
        //将PlanOperateData转成List<NodeVo>
        List<NodeVo> nodeVoList = data.getDevices();
        nodeVoList.addAll(data.getEdges());

        //将nodeVo转成node并返回
        List<Node> nodeList = toNodes(nodeVoList);
        return nodeList;

    }

    public static String toPlanOperateDataStr(List<Node> nodes) {
        return JSON.toJSONString(toPlanOperateData(nodes));
    }

    /**
     * 转成方案操作数据节点
     *
     * @return
     */
    public static String toOperateDataStr(List<LayNodeBo> sureLays) {
        ArrayList<Node> allNodes = new ArrayList<>();
        // 将所有的放置点数据拼接在一起
        for (LayNodeBo sureLay : sureLays) {
            allNodes.addAll(sureLay.getAllNode());
        }
        return toPlanOperateDataStr(allNodes);
    }

    /**
     * 是否未使用的节点
     * 判断是否有“预留” || “备用” 名称即可
     */
    public static boolean isNotUsed(Node node) {
        String psrName = node.getPsrName();
        if (StringUtils.isBlank(psrName)) {
            return false;
        }
        return psrName.contains("预留") || psrName.contains("备用");
    }

    /**
     * 放在位置类型转为方案类型
     */
    public static String layTypeToPlanType(String layType) {
        return PlanConstants.layToPlanType.get(layType);
    }

    /**
     * 获取剩余间隔的开关
     *
     * @param station 站房节点
     */
    public static List<Node> getSurplusBayEndNode(Node station) {
        List<Node> children = station.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return null;
        }
        // 获取所有包含备用的开关
        List<Node> backupKgs = children.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());

        // 遍历备用开关 获取末端节点的开关（两个开关在同一个间隔的情况）
        ArrayList<Node> result = new ArrayList<>();

        for (Node backupKg : backupKgs) {
            Node bayEndNode = findBayEndNode(backupKg, new HashMap<>());
            result.add(bayEndNode == null ? backupKg : bayEndNode);
        }
        return result;
    }

    /**
     * 查找站房内 间隔末端设备
     * TODO（我们这里暂时设置开关，后续可能是终端了）
     */
    public static Node findBayEndNode(Node node, HashMap<String, Boolean> useNodeMap) {
        Node currentNode = node;

        Node result = null;

        // 递归获取查找
        while (currentNode != null) {
            useNodeMap.put(currentNode.getId(), true);

            // 新增当前的线路
            boolean isEdge = currentNode.isEdge();

            if (currentNode.isBus()) {
                return null;
            }
            if (currentNode.isKg("all")) {
                result = currentNode;
            }
            Node nextNode = null;

            List<Node> edges = null;
            if (isEdge) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = useNodeMap.containsKey(source.getId()) ? target : source;
                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }
            } else { // 设备节点
                // 当前设备有那些边 我们继续往下递归
                edges = currentNode.getEdges();
            }
            if (edges != null) {
                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());

                // 调度继续循环
                for (Node edge : edges) {
                    Node tmp = findBayEndNode(edge, useNodeMap);
                    if (tmp != null) {
                        return tmp;
                    }
                }
                nextNode = null;
            }
            currentNode = nextNode;
        }
        return result;
    }

    // 获取这条可以放在有剩余间隔的站房
    public static List<HwgBayBo> getHwgStation(List<Node> paths) {

        if (CollectionUtils.isEmpty(paths)) {
            return null;
        }

        List<Node> hwgs = new ArrayList<>();

        for (Node node : paths) {
            Node parent = node.getParent();
            if (parent != null && parent.isHwg()) {
                hwgs.add(parent);
            }
        }
        // 去重
        ListUtils.distinctInPlace(hwgs, Node::getPsrId);

        Node firstParent = paths.get(0).getParent();

        // 过滤第一个为节点的环网柜
        if (firstParent != null && firstParent.isHwg()) {
            hwgs = hwgs.stream().filter(n -> !n.equals(firstParent)).collect(Collectors.toList());
        }
        List<HwgBayBo> result = hwgs.stream().map(hwg -> {
            List<Node> bayNodes = NodeUtils.getSurplusBayEndNode(hwg);
            return new HwgBayBo(hwg, bayNodes);
        }).collect(Collectors.toList());

        return result;
    }

    /**
     * 当前节点路径 是否存在某个节点
     */
    public static int indexPathNode(ArrayList<Node> nodePaths, List<Node> someNodes) {
        for (int i = 0; i < nodePaths.size(); i++) {
            Node node = nodePaths.get(i);
            // 当前节点
            if (someNodes.stream().anyMatch(n -> n.equals(node.getId()))) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 获取重叠的节点集合
     *
     * @param nodes1 节点集合1
     * @param nodes2 另一个节点集合
     */
    public static ArrayList<Node> getOverlapNodePaths(ArrayList<Node> nodes1, ArrayList<Node> nodes2) {
        ArrayList<Node> result = new ArrayList<>();
        int minSize = Math.min(nodes1.size(), nodes2.size());

        for (int i = 0; i < minSize; i++) {
            if (nodes1.get(i).equals(nodes2.get(i).getId())) {
                result.add(nodes1.get(i));
            } else {
                break;
            }
        }
        return result;
    }

    public static void main(String[] args) {
        GeometryFactory geometryFactory = new GeometryFactory();
        double[][] coords = {{118, 31},
                {119, 32}};
        Coordinate[] collectList = Arrays.stream(coords)
                .map(lngLat -> new Coordinate(lngLat[0], lngLat[1]))
                .toArray(Coordinate[]::new);
        LineString lineString = geometryFactory.createLineString(collectList);

        Point point = geometryFactory.createPoint(new Coordinate(118.1, 31.1));

        Node edge = new Node("aa", lineString);
        edge.setEdge(true);
        Node device = new Node("bb", point);

        List<Node> list = Arrays.asList(edge, device);
        List<Node> nodes = NodeUtils.copyNodes(list);
        System.out.println(nodes);
    }
}

