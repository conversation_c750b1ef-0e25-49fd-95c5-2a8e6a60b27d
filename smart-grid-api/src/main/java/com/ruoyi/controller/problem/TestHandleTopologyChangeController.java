package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.problem.ProblemSchemeMapper;
import com.ruoyi.service.znap.IZnapTopologyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 网架结构变更控制器
 */
@RestController
@RequestMapping("/handleTopologyChange")
@SaIgnore
public class TestHandleTopologyChangeController {

    @Autowired
    private IZnapTopologyService znapTopologyService;

    @Autowired
    private ProblemSchemeMapper problemSchemeMapper;

    /**
     * 获取网架拓扑分析结果
     */
    @GetMapping("/handleTopologyChange")
    public R<SingAnalysis> handleTopologyChange(@RequestParam String feederId) throws JsonProcessingException {
        // 获取拓扑结构
        String jsonStr = problemSchemeMapper.selectById(31707252L).getOperateData();
        generateNodes(jsonStr, feederId);
        return R.ok();
    }

    /**
     * 处理网架变更
     * @param jsonStr 变更JSON数据
     * @param pmsFeederId 馈线ID
     * @return 变更后的拓扑结构节点列表
     * @throws JsonProcessingException JSON解析异常
     */
    public List<Node> generateNodes(String jsonStr, String pmsFeederId) throws JsonProcessingException {
        ZnapTopology topology = znapTopologyService.generateNode(pmsFeederId);
        List<Node> nodes = NodeUtils.planOperateJsonToNodes(jsonStr);

        Map<String, Node> nodeMap = topology.getNodeMap();
        ArrayList<Node> nodeList = topology.getNodeList();

        // 第一步：处理非边节点（设备节点）
        processDeviceNodes(nodes, nodeMap, nodeList);

        // 第二步：处理新增连接线
        processEdgeNodes(nodes, nodeMap, nodeList);

        return nodeList;
    }

    /**
     * 处理设备节点
     * @param nodes 所有节点
     * @param nodeMap 节点映射表
     * @param nodeList 节点列表
     */
    private void processDeviceNodes(List<Node> nodes, Map<String, Node> nodeMap, ArrayList<Node> nodeList) {
        for (Node node : nodes) {
            if (!node.isEdge()) {
                if (!nodeMap.containsKey(node.getPsrId())) {
                    nodeList.add(node);
                    nodeMap.put(node.getId(), node);
                }
            }
        }
    }

    /**
     * 处理边节点
     * @param nodes 所有节点
     * @param nodeMap 节点映射表
     * @param nodeList 节点列表
     */
    private void processEdgeNodes(List<Node> nodes, Map<String, Node> nodeMap, ArrayList<Node> nodeList) {
        for (Node node : nodes) {
            // 自定义新增的设备
            //  1、添加到集合里面 nodeList和nodeMap

            // 2、连接关系处理 目前只有新增联络线和住上开关
            if (node.isEdge()) {
                Node source = node.getSource();
                // 如果链接的是电网设备  需要和整体基础的网价更改
                if (!node.isPsrNode()) {
                    processCustomEdgeNode(node, source, nodeMap, nodeList);
                }
            } else {
                // 节点形式后续有可能有其它特殊的 目前暂时没有  先不管
            }
        }
    }

    /**
     * 处理自定义边节点
     * @param node 边节点
     * @param source 源节点
     * @param nodeMap 节点映射表
     * @param nodeList 节点列表
     */
    private void processCustomEdgeNode(Node node, Node source, Map<String, Node> nodeMap, ArrayList<Node> nodeList) {
        // 这里的source和基础网架里面的source实际是同一个设备  但不是同一个对象
        Node baseSource = nodeMap.get(source.getPsrId());

        // 删除原有连接
        removeExistingEdgesFromBaseSource(baseSource);

        // 新建连接关系
        baseSource.addEdge(node, true);

        // 添加边节点到集合
        if (!nodeMap.containsKey(node.getPsrId())) {
            nodeList.add(node);
            nodeMap.put(node.getPsrId(), node);
        }
    }

    /**
     * 删除基础源节点的原有连接
     * @param baseSource 基础源节点
     */
    private void removeExistingEdgesFromBaseSource(Node baseSource) {
        if (baseSource != null) {
            // 先收集要删除的边
            List<Node> edgesToRemove = new ArrayList<>();
            for (Node e : baseSource.getEdges()) {
                if (e.getSource().getPsrId().equals(baseSource.getPsrId())) {
                    edgesToRemove.add(e);
                }
            }
            for (Node e : edgesToRemove) {
                baseSource.removeEdge(e, true);
            }
        }
    }

}