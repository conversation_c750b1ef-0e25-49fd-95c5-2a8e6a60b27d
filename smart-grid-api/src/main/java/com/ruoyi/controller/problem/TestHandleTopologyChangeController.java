package com.ruoyi.controller.problem;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.problem.ProblemSchemeMapper;
import com.ruoyi.service.znap.IZnapTopologyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 网架结构变更控制器
 */
@RestController
@RequestMapping("/handleTopologyChange")
@SaIgnore
public class TestHandleTopologyChangeController {

    @Autowired
    private IZnapTopologyService znapTopologyService;

    @Autowired
    private ProblemSchemeMapper problemSchemeMapper;

    /**
     * 获取网架拓扑分析结果
     */
    @GetMapping("/handleTopologyChange")
    public R<SingAnalysis> handleTopologyChange(@RequestParam String feederId) throws JsonProcessingException {
        // 获取拓扑结构
        String jsonStr = problemSchemeMapper.selectById(31707252L).getOperateData();
        generateNodes(jsonStr, feederId);
        return R.ok();
    }

    /**
     * 处理网架变更
     * @param pmsFeederId 馈线ID
     * @param jsonStr 变更JSON
     * @return 变更后的拓扑结构
     */
    public List<Node> generateNodes(String jsonStr, String pmsFeederId) throws JsonProcessingException {
        ZnapTopology topology = znapTopologyService.generateNode(pmsFeederId);
        List<Node> nodes = NodeUtils.planOperateJsonToNodes(jsonStr);

        Map<String, Node> nodeMap = topology.getNodeMap();
        ArrayList<Node> nodeList = topology.getNodeList();
        for (Node node : nodes) {
            if (!node.isEdge()) {
                if (!nodeMap.containsKey(node.getPsrId())) {
                    nodeList.add(node);
                    nodeMap.put(node.getPsrId(), node);
                }
            }
        }
        // 处理新增连接线
        for (Node node : nodes) {
            // 自定义新增的设备
            //  1、添加到集合里面 nodeList和nodeMap

            // 2、连接关系处理 目前只有新增联络线和住上开关
            if (node.isEdge()) {
                Node source = node.getSource();
                // 如果链接的是电网设备  需要和整体基础的网价更改
                if (!node.isPsrNode()) {
                    // 这里的source和基础网架里面的source实际是同一个设备  但不是同一个对象
                    Node baseSource = nodeMap.get(source.getPsrId());
                    // 删除原有连接
                    if (baseSource != null) {
                        // 先收集要删除的边
                        List<Node> edgesToRemove = new ArrayList<>();
                        for (Node e : baseSource.getEdges()) {
                            if (e.getSource().getPsrId().equals(baseSource.getPsrId())) {
                                edgesToRemove.add(e);
                            }
                        }
                        for (Node e : edgesToRemove) {
                            baseSource.removeEdge(e, true);
                        }
                    }
                    // 新建连接关系
                    baseSource.addEdge(node, true);
                    if (!nodeMap.containsKey(node.getPsrId())) {
                        nodeList.add(node);
                        nodeMap.put(node.getPsrId(), node);
                    }
                }
            } else {
                // 节点形式后续有可能有其它特殊的 目前暂时没有  先不管
            }

        }
        return nodeList;
    }

}