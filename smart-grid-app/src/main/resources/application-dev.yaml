--- # 安全配置
security:
  excludes:
    - /magic/**
--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
znap-service:
  powerRangeUrl: http://**************:8184/trend_calc/system/trend/powerRange
  calcUrl: http://**************:8184/trend_calc/system/trend/clac
spring:
  redis:
    # 地址
    host: **************
#    host: 127.0.0.1
#    host: *************
    # 端口，默认为6379
#    port: 6379
    port: 6077
    # 数据库索引
    database: 1
#    password: 123456
    password: spueo0tIzzwkAvAfIj30ROpJA8aM6UJz

    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false
--- # 数据源配置（按照设计应用开发模块只需要设计自己的yml配置中数据源参数，其他都统一设计，便于按照微服务方式统一部署）
spring:
  datasource:
#     type: com.alibaba.druid.pool.DruidDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
#    sql监控 http://localhost:8080/smart-grid/druid/index.html
    druid:
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
        resetEnable: false
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          driverClassName: org.postgresql.Driver
          url: ********************************************************************
#          url: *********************************************
          username: postgres
          password: f89RgEVgruvau0T5DhXKOjKwQ1Lgqe7P
#          password: 123456
        slave:
          lazy: true
          driverClassName: org.postgresql.Driver
          url: *****************************************************************
          username: znap_bs
          password: znap@2022_zhrj
#          url: ******************************************************************
#          username: postgres
#          password: f89RgEVgruvau0T5DhXKOjKwQ1Lgqe7P
--- # Mybatis Plus 配置
#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

--- # magic-api 配置
magic-api:
  # 配置web开发页面，可空，为空时不开启
  web: /magic/web
